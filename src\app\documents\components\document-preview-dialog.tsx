'use client';

import { useState, useEffect, useCallback } from 'react';
import { X, Download, Loader2 } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { DocumentTemplate } from '@/features/document/types';

interface DocumentPreviewDialogProps {
  template: DocumentTemplate | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

type ViewerMode = 'mammoth' | 'office' | 'download';

export function DocumentPreviewDialog({
  template,
  open,
  onOpenChange,
}: Readonly<DocumentPreviewDialogProps>) {
  const [isLoading, setIsLoading] = useState(false);
  const [previewHtml, setPreviewHtml] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [viewerMode, setViewerMode] = useState<ViewerMode>('mammoth');

  const loadPreview = useCallback(async () => {
    if (!template) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/templates/${template.id}/preview`);

      if (!response.ok) {
        throw new Error('Error al cargar la vista previa');
      }

      const html = await response.text();
      setPreviewHtml(html);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setIsLoading(false);
    }
  }, [template]);

  useEffect(() => {
    if (open && template) {
      loadPreview();
    } else {
      setPreviewHtml(null);
      setError(null);
    }
  }, [open, template, loadPreview]);

  const handleDownload = () => {
    if (template) {
      const url = `/api/templates/${template.id}/download`;
      const link = document.createElement('a');
      link.href = url;
      link.download = template.fileName || `${template.name}.docx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleClose = () => {
    onOpenChange(false);
  };

  const getFileExtension = (fileName: string) => {
    return fileName.split('.').pop()?.toLowerCase() || '';
  };

  const getOfficeViewerUrl = () => {
    if (!template) return '';
    const downloadUrl = `${window.location.origin}/api/templates/${template.id}/download`;
    return `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(downloadUrl)}`;
  };

  const renderMammothViewer = () => (
    <div className="h-full">
      {isLoading && (
        <div className="flex h-96 items-center justify-center">
          <div className="text-center">
            <Loader2 className="mx-auto h-8 w-8 animate-spin text-blue-600" />
            <p className="mt-2 text-sm text-gray-600">
              Cargando vista previa...
            </p>
          </div>
        </div>
      )}

      {error && (
        <div className="flex h-96 items-center justify-center">
          <div className="text-center">
            <p className="font-medium text-red-600">Error</p>
            <p className="mt-1 text-sm text-gray-600">{error}</p>
            <Button
              variant="outline"
              size="sm"
              onClick={loadPreview}
              className="mt-4"
            >
              Reintentar
            </Button>
          </div>
        </div>
      )}

      {previewHtml && !isLoading && !error && (
        <div className="h-[75vh] overflow-auto rounded-lg border bg-white p-6">
          <div
            className="prose prose-sm max-w-none font-sans leading-relaxed text-gray-800"
            dangerouslySetInnerHTML={{ __html: previewHtml }}
          />
        </div>
      )}
    </div>
  );

  const renderOfficeViewer = () => {
    const fileExtension = getFileExtension(template?.fileName || '');
    const supportedFormats = ['docx', 'xlsx', 'pptx', 'doc', 'xls', 'ppt'];

    if (!supportedFormats.includes(fileExtension)) {
      return (
        <div className="flex h-96 items-center justify-center">
          <div className="text-center">
            <p className="font-medium text-orange-600">Formato no soportado</p>
            <p className="mt-1 text-sm text-gray-600">
              El visor de Office Online no soporta archivos .{fileExtension}
            </p>
            <p className="mt-2 text-sm text-gray-500">
              Prueba con el visor Mammoth o descarga el archivo
            </p>
          </div>
        </div>
      );
    }

    return (
      <div className="h-[75vh] w-full">
        <iframe
          src={getOfficeViewerUrl()}
          className="h-full w-full rounded-lg border"
          title={`Vista previa de ${template?.name}`}
          onLoad={() => setIsLoading(false)}
          onError={() => {
            setError('Error al cargar el visor de Office Online');
            setIsLoading(false);
          }}
        />
      </div>
    );
  };

  if (!template) return null;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="h-[95vh] max-h-[95vh] w-[99vw] max-w-[99vw] overflow-hidden">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="text-lg font-semibold">
              Vista Previa: {template.name}
            </DialogTitle>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleDownload}
                disabled={isLoading}
              >
                <Download className="mr-2 h-4 w-4" />
                Descargar
              </Button>
              <Button variant="ghost" size="sm" onClick={handleClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          <Tabs
            value={viewerMode}
            onValueChange={(value) => setViewerMode(value as ViewerMode)}
            className="h-full"
          >
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="mammoth">Mammoth Viewer</TabsTrigger>
              <TabsTrigger value="office">Office Online</TabsTrigger>
              <TabsTrigger value="download">Solo Descarga</TabsTrigger>
            </TabsList>

            <TabsContent value="mammoth" className="mt-4 h-[calc(100%-3rem)]">
              {renderMammothViewer()}
            </TabsContent>

            <TabsContent value="office" className="mt-4 h-[calc(100%-3rem)]">
              {renderOfficeViewer()}
            </TabsContent>

            <TabsContent value="download" className="mt-4 h-[calc(100%-3rem)]">
              <div className="flex h-full items-center justify-center">
                <div className="text-center">
                  <Download className="mx-auto h-16 w-16 text-gray-400" />
                  <h3 className="mt-4 text-lg font-medium text-gray-900">
                    Descarga el documento
                  </h3>
                  <p className="mt-2 text-sm text-gray-600">
                    Haz clic en el botón de descarga para obtener el archivo
                    original
                  </p>
                  <Button onClick={handleDownload} className="mt-4" size="lg">
                    <Download className="mr-2 h-5 w-5" />
                    Descargar {template.fileName}
                  </Button>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <div className="mt-4 rounded-lg bg-gray-50 p-4">
            <h4 className="mb-2 font-medium text-gray-900">
              Información del Documento
            </h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700">Nombre:</span>
                <span className="ml-2 text-gray-600">{template.name}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">Categoría:</span>
                <span className="ml-2 text-gray-600">{template.category}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">Archivo:</span>
                <span className="ml-2 text-gray-600">{template.fileName}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">Placeholders:</span>
                <span className="ml-2 text-gray-600">
                  {template.placeholders?.length || 0} campos
                </span>
              </div>
            </div>
            {template.description && (
              <div className="mt-3">
                <span className="font-medium text-gray-700">Descripción:</span>
                <p className="mt-1 text-gray-600">{template.description}</p>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
