import { NextRequest, NextResponse } from 'next/server';
import mammoth from 'mammoth';
import { downloadDocumentTemplate } from '@/features/document/actions';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;
    const [result] = await downloadDocumentTemplate({ id });

    if (!result) {
      return NextResponse.json(
        { error: 'Plantilla no encontrada' },
        { status: 404 },
      );
    }

    const { buffer } = result;

    // Convertir documento Word a HTML usando mammoth
    const htmlResult = await mammoth.convertToHtml({ buffer });

    // Return only the content HTML without full document structure
    // This prevents nested HTML issues when used with dangerouslySetInnerHTML
    const contentHtml = htmlResult.value;

    return new NextResponse(contentHtml, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
        'Cache-Control': 'public, max-age=3600',
      },
    });
  } catch (error) {
    console.error('Error generando vista previa:', error);

    // Return simple error content without full HTML structure
    const errorContent = `
      <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
        <div style="font-size: 48px; color: #e74c3c; margin-bottom: 20px;">⚠️</div>
        <h1 style="color: #333; margin-bottom: 10px;">Error al generar vista previa</h1>
        <p style="color: #666; line-height: 1.6;">No se pudo generar la vista previa del documento. Esto puede ocurrir si el archivo no es un documento Word válido o si hay problemas de conectividad.</p>
        <p style="color: #666; line-height: 1.6;">Puedes intentar descargar el archivo directamente para verificar su contenido.</p>
      </div>
    `;

    return new NextResponse(errorContent, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
      },
      status: 500,
    });
  }
}
